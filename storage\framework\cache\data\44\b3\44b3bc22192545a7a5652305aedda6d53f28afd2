1751633065O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:216:{i:0;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:4:"type";s:21:"home_default_currency";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:05:52";s:10:"updated_at";s:19:"2019-01-28 06:56:53";}s:11:" * original";a:6:{s:2:"id";i:1;s:4:"type";s:21:"home_default_currency";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:05:52";s:10:"updated_at";s:19:"2019-01-28 06:56:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"type";s:23:"system_default_currency";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:06:58";s:10:"updated_at";s:19:"2025-04-09 15:28:08";}s:11:" * original";a:6:{s:2:"id";i:2;s:4:"type";s:23:"system_default_currency";s:5:"value";s:2:"28";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-16 07:06:58";s:10:"updated_at";s:19:"2025-04-09 15:28:08";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:3;s:4:"type";s:15:"currency_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2018-10-17 08:31:59";}s:11:" * original";a:6:{s:2:"id";i:3;s:4:"type";s:15:"currency_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2018-10-17 08:31:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:4:"type";s:13:"symbol_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2019-01-20 07:40:55";}s:11:" * original";a:6:{s:2:"id";i:4;s:4:"type";s:13:"symbol_format";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2019-01-20 07:40:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:5;s:4:"type";s:14:"no_of_decimals";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2020-03-04 06:27:16";}s:11:" * original";a:6:{s:2:"id";i:5;s:4:"type";s:14:"no_of_decimals";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-17 08:31:59";s:10:"updated_at";s:19:"2020-03-04 06:27:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:6;s:4:"type";s:18:"product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 07:08:37";s:10:"updated_at";s:19:"2019-02-04 06:41:41";}s:11:" * original";a:6:{s:2:"id";i:6;s:4:"type";s:18:"product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 07:08:37";s:10:"updated_at";s:19:"2019-02-04 06:41:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:7;s:4:"type";s:24:"vendor_system_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:16";s:10:"updated_at";s:19:"2019-02-04 06:41:38";}s:11:" * original";a:6:{s:2:"id";i:7;s:4:"type";s:24:"vendor_system_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:16";s:10:"updated_at";s:19:"2019-02-04 06:41:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:7;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:8;s:4:"type";s:12:"show_vendors";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:47";s:10:"updated_at";s:19:"2019-02-04 06:41:13";}s:11:" * original";a:6:{s:2:"id";i:8;s:4:"type";s:12:"show_vendors";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:14:47";s:10:"updated_at";s:19:"2019-02-04 06:41:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:8;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:11;s:4:"type";s:12:"cash_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:05";s:10:"updated_at";s:19:"2025-04-15 15:11:04";}s:11:" * original";a:6:{s:2:"id";i:11;s:4:"type";s:12:"cash_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:05";s:10:"updated_at";s:19:"2025-04-15 15:11:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:9;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:12;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:27";s:10:"updated_at";s:19:"2019-03-05 11:11:36";}s:11:" * original";a:6:{s:2:"id";i:12;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2018-10-28 13:16:27";s:10:"updated_at";s:19:"2019-03-05 11:11:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:10;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:13;s:4:"type";s:12:"best_selling";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-12-24 13:43:44";s:10:"updated_at";s:19:"2019-02-14 10:59:13";}s:11:" * original";a:6:{s:2:"id";i:13;s:4:"type";s:12:"best_selling";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2018-12-24 13:43:44";s:10:"updated_at";s:19:"2019-02-14 10:59:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:11;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:14;s:4:"type";s:14:"paypal_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-01-16 18:14:18";}s:11:" * original";a:6:{s:2:"id";i:14;s:4:"type";s:14:"paypal_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-01-16 18:14:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:12;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:15;s:4:"type";s:18:"sslcommerz_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-03-14 05:37:26";}s:11:" * original";a:6:{s:2:"id";i:15;s:4:"type";s:18:"sslcommerz_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-16 18:14:18";s:10:"updated_at";s:19:"2019-03-14 05:37:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:13;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:17;s:4:"type";s:17:"vendor_commission";s:5:"value";s:1:"5";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-31 11:48:04";s:10:"updated_at";s:19:"2025-04-11 15:54:38";}s:11:" * original";a:6:{s:2:"id";i:17;s:4:"type";s:17:"vendor_commission";s:5:"value";s:1:"5";s:4:"lang";N;s:10:"created_at";s:19:"2019-01-31 11:48:04";s:10:"updated_at";s:19:"2025-04-11 15:54:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:14;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:18;s:4:"type";s:17:"verification_form";s:5:"value";s:278:"[{"type":"text","label":"Your name"},{"type":"text","label":"Manufacturer name"},{"type":"text","label":"Email"},{"type":"text","label":"Manufacturer GST Number"},{"type":"text","label":"Full Address"},{"type":"text","label":"Phone Number"},{"type":"file","label":"Tax Papers"}]";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-03 17:06:58";s:10:"updated_at";s:19:"2025-04-09 16:21:30";}s:11:" * original";a:6:{s:2:"id";i:18;s:4:"type";s:17:"verification_form";s:5:"value";s:278:"[{"type":"text","label":"Your name"},{"type":"text","label":"Manufacturer name"},{"type":"text","label":"Email"},{"type":"text","label":"Manufacturer GST Number"},{"type":"text","label":"Full Address"},{"type":"text","label":"Phone Number"},{"type":"file","label":"Tax Papers"}]";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-03 17:06:58";s:10:"updated_at";s:19:"2025-04-09 16:21:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:15;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:19;s:4:"type";s:16:"google_analytics";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-06 17:52:35";s:10:"updated_at";s:19:"2024-09-13 17:09:30";}s:11:" * original";a:6:{s:2:"id";i:19;s:4:"type";s:16:"google_analytics";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-06 17:52:35";s:10:"updated_at";s:19:"2024-09-13 17:09:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:16;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:20;s:4:"type";s:14:"facebook_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:21:59";s:10:"updated_at";s:19:"2019-02-09 01:11:15";}s:11:" * original";a:6:{s:2:"id";i:20;s:4:"type";s:14:"facebook_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:21:59";s:10:"updated_at";s:19:"2019-02-09 01:11:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:17;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:21;s:4:"type";s:12:"google_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:10";s:10:"updated_at";s:19:"2025-01-02 17:08:35";}s:11:" * original";a:6:{s:2:"id";i:21;s:4:"type";s:12:"google_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:10";s:10:"updated_at";s:19:"2025-01-02 17:08:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:18;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:22;s:4:"type";s:13:"twitter_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:20";s:10:"updated_at";s:19:"2019-02-08 08:02:56";}s:11:" * original";a:6:{s:2:"id";i:22;s:4:"type";s:13:"twitter_login";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-02-07 18:22:20";s:10:"updated_at";s:19:"2019-02-08 08:02:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:19;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:23;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 17:08:17";}s:11:" * original";a:6:{s:2:"id";i:23;s:4:"type";s:17:"payumoney_payment";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 17:08:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:20;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:24;s:4:"type";s:17:"payumoney_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 11:09:18";}s:11:" * original";a:6:{s:2:"id";i:24;s:4:"type";s:17:"payumoney_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-03-05 17:08:17";s:10:"updated_at";s:19:"2019-03-05 11:09:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:21;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:36;s:4:"type";s:13:"facebook_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-15 17:15:04";s:10:"updated_at";s:19:"2019-04-15 17:15:04";}s:11:" * original";a:6:{s:2:"id";i:36;s:4:"type";s:13:"facebook_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-15 17:15:04";s:10:"updated_at";s:19:"2019-04-15 17:15:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:22;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:37;s:4:"type";s:18:"email_verification";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-30 13:00:07";s:10:"updated_at";s:19:"2024-09-14 11:45:26";}s:11:" * original";a:6:{s:2:"id";i:37;s:4:"type";s:18:"email_verification";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-04-30 13:00:07";s:10:"updated_at";s:19:"2024-09-14 11:45:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:23;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:38;s:4:"type";s:13:"wallet_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-05-19 13:35:44";s:10:"updated_at";s:19:"2025-04-15 15:11:30";}s:11:" * original";a:6:{s:2:"id";i:38;s:4:"type";s:13:"wallet_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-05-19 13:35:44";s:10:"updated_at";s:19:"2025-04-15 15:11:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:24;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:39;s:4:"type";s:13:"coupon_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2025-04-15 15:11:42";}s:11:" * original";a:6:{s:2:"id";i:39;s:4:"type";s:13:"coupon_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2025-04-15 15:11:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:25;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:40;s:4:"type";s:15:"current_version";s:5:"value";s:5:"9.8.1";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2019-06-11 15:16:18";}s:11:" * original";a:6:{s:2:"id";i:40;s:4:"type";s:15:"current_version";s:5:"value";s:5:"9.8.1";s:4:"lang";N;s:10:"created_at";s:19:"2019-06-11 15:16:18";s:10:"updated_at";s:19:"2019-06-11 15:16:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:26;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:42;s:4:"type";s:17:"instamojo_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2019-07-06 15:28:43";}s:11:" * original";a:6:{s:2:"id";i:42;s:4:"type";s:17:"instamojo_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-07-06 15:28:43";s:10:"updated_at";s:19:"2019-07-06 15:28:43";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:27;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:45;s:4:"type";s:12:"pickup_point";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:20:39";s:10:"updated_at";s:19:"2025-06-09 18:31:03";}s:11:" * original";a:6:{s:2:"id";i:45;s:4:"type";s:12:"pickup_point";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:20:39";s:10:"updated_at";s:19:"2025-06-09 18:31:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:28;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:46;s:4:"type";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:04";s:10:"updated_at";s:19:"2019-10-17 17:21:04";}s:11:" * original";a:6:{s:2:"id";i:46;s:4:"type";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:04";s:10:"updated_at";s:19:"2019-10-17 17:21:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:29;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:48;s:4:"type";s:16:"voguepay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:38";s:10:"updated_at";s:19:"2019-10-17 17:21:38";}s:11:" * original";a:6:{s:2:"id";i:48;s:4:"type";s:16:"voguepay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2019-10-17 17:21:38";s:10:"updated_at";s:19:"2019-10-17 17:21:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:30;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:50;s:4:"type";s:24:"category_wise_commission";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:52:47";s:10:"updated_at";s:19:"2020-01-21 12:52:47";}s:11:" * original";a:6:{s:2:"id";i:50;s:4:"type";s:24:"category_wise_commission";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:52:47";s:10:"updated_at";s:19:"2020-01-21 12:52:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:31;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:51;s:4:"type";s:19:"conversation_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:53:21";s:10:"updated_at";s:19:"2020-01-21 12:53:21";}s:11:" * original";a:6:{s:2:"id";i:51;s:4:"type";s:19:"conversation_system";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-21 12:53:21";s:10:"updated_at";s:19:"2020-01-21 12:53:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:32;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:52;s:4:"type";s:21:"guest_checkout_active";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 13:06:38";s:10:"updated_at";s:19:"2020-01-22 13:06:38";}s:11:" * original";a:6:{s:2:"id";i:52;s:4:"type";s:21:"guest_checkout_active";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 13:06:38";s:10:"updated_at";s:19:"2020-01-22 13:06:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:33;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:53;s:4:"type";s:14:"facebook_pixel";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 17:13:58";s:10:"updated_at";s:19:"2020-01-22 17:13:58";}s:11:" * original";a:6:{s:2:"id";i:53;s:4:"type";s:14:"facebook_pixel";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-01-22 17:13:58";s:10:"updated_at";s:19:"2020-01-22 17:13:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:34;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:55;s:4:"type";s:18:"classified_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-05-13 18:31:05";s:10:"updated_at";s:19:"2025-04-15 15:11:38";}s:11:" * original";a:6:{s:2:"id";i:55;s:4:"type";s:18:"classified_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-05-13 18:31:05";s:10:"updated_at";s:19:"2025-04-15 15:11:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:35;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:56;s:4:"type";s:25:"pos_activation_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-06-11 15:15:02";s:10:"updated_at";s:19:"2020-06-11 15:15:02";}s:11:" * original";a:6:{s:2:"id";i:56;s:4:"type";s:25:"pos_activation_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-06-11 15:15:02";s:10:"updated_at";s:19:"2020-06-11 15:15:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:36;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:57;s:4:"type";s:13:"shipping_type";s:5:"value";s:21:"product_wise_shipping";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:11:" * original";a:6:{s:2:"id";i:57;s:4:"type";s:13:"shipping_type";s:5:"value";s:21:"product_wise_shipping";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:37;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:58;s:4:"type";s:23:"flat_rate_shipping_cost";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:11:" * original";a:6:{s:2:"id";i:58;s:4:"type";s:23:"flat_rate_shipping_cost";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:38;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:59;s:4:"type";s:19:"shipping_cost_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:11:" * original";a:6:{s:2:"id";i:59;s:4:"type";s:19:"shipping_cost_admin";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-01 19:19:56";s:10:"updated_at";s:19:"2020-07-01 19:19:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:39;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:60;s:4:"type";s:15:"payhere_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:11:" * original";a:6:{s:2:"id";i:60;s:4:"type";s:15:"payhere_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-07-30 23:53:53";s:10:"updated_at";s:19:"2020-07-30 23:53:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:40;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:62;s:4:"type";s:16:"google_recaptcha";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-08-17 12:43:37";s:10:"updated_at";s:19:"2024-09-13 17:25:00";}s:11:" * original";a:6:{s:2:"id";i:62;s:4:"type";s:16:"google_recaptcha";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2020-08-17 12:43:37";s:10:"updated_at";s:19:"2024-09-13 17:25:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:41;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:64;s:4:"type";s:11:"header_logo";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-09 16:40:30";}s:11:" * original";a:6:{s:2:"id";i:64;s:4:"type";s:11:"header_logo";s:5:"value";s:1:"2";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-09 16:40:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:42;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:65;s:4:"type";s:22:"show_language_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:65;s:4:"type";s:22:"show_language_switcher";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:43;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:66;s:4:"type";s:22:"show_currency_switcher";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-11 16:33:58";}s:11:" * original";a:6:{s:2:"id";i:66;s:4:"type";s:22:"show_currency_switcher";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-11 16:33:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:44;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:67;s:4:"type";s:13:"header_stikcy";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-11 16:33:41";}s:11:" * original";a:6:{s:2:"id";i:67;s:4:"type";s:13:"header_stikcy";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-11 16:33:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:45;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:68;s:4:"type";s:11:"footer_logo";s:5:"value";s:3:"287";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-18 10:48:21";}s:11:" * original";a:6:{s:2:"id";i:68;s:4:"type";s:11:"footer_logo";s:5:"value";s:3:"287";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-18 10:48:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:46;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:69;s:4:"type";s:20:"about_us_description";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:69;s:4:"type";s:20:"about_us_description";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:47;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:70;s:4:"type";s:15:"contact_address";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:70;s:4:"type";s:15:"contact_address";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:48;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:71;s:4:"type";s:13:"contact_phone";s:5:"value";s:13:"+************";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-18 10:50:25";}s:11:" * original";a:6:{s:2:"id";i:71;s:4:"type";s:13:"contact_phone";s:5:"value";s:13:"+************";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-18 10:50:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:49;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:72;s:4:"type";s:13:"contact_email";s:5:"value";s:18:"<EMAIL>";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:15:29";}s:11:" * original";a:6:{s:2:"id";i:72;s:4:"type";s:13:"contact_email";s:5:"value";s:18:"<EMAIL>";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:50;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:73;s:4:"type";s:17:"widget_one_labels";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:73;s:4:"type";s:17:"widget_one_labels";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:51;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:74;s:4:"type";s:16:"widget_one_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:74;s:4:"type";s:16:"widget_one_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:52;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:75;s:4:"type";s:10:"widget_one";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:75;s:4:"type";s:10:"widget_one";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:53;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:76;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:76;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:54;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:77;s:4:"type";s:17:"show_social_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:77;s:4:"type";s:17:"show_social_links";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:55;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:78;s:4:"type";s:13:"facebook_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:78;s:4:"type";s:13:"facebook_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:56;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:79;s:4:"type";s:12:"twitter_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:79;s:4:"type";s:12:"twitter_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:57;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:80;s:4:"type";s:14:"instagram_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:80;s:4:"type";s:14:"instagram_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:58;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:81;s:4:"type";s:12:"youtube_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:81;s:4:"type";s:12:"youtube_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:59;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:82;s:4:"type";s:13:"linkedin_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:82;s:4:"type";s:13:"linkedin_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:60;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:83;s:4:"type";s:21:"payment_method_images";s:5:"value";s:2:"12";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:11:" * original";a:6:{s:2:"id";i:83;s:4:"type";s:21:"payment_method_images";s:5:"value";s:2:"12";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:61;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:84;s:4:"type";s:18:"home_slider_images";s:5:"value";s:19:"["872","875","874"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-12 11:24:15";}s:11:" * original";a:6:{s:2:"id";i:84;s:4:"type";s:18:"home_slider_images";s:5:"value";s:19:"["872","875","874"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-04-12 11:24:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:62;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:85;s:4:"type";s:17:"home_slider_links";s:5:"value";s:142:"["https:\/\/netbazzar.com\/seller\/login","https:\/\/netbazzar.com\/shop\/registration\/verification","https:\/\/netbazzar.com\/users\/login"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:26:52";}s:11:" * original";a:6:{s:2:"id";i:85;s:4:"type";s:17:"home_slider_links";s:5:"value";s:142:"["https:\/\/netbazzar.com\/seller\/login","https:\/\/netbazzar.com\/shop\/registration\/verification","https:\/\/netbazzar.com\/users\/login"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:26:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:63;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:86;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:13:"["920","919"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-17 16:39:01";}s:11:" * original";a:6:{s:2:"id";i:86;s:4:"type";s:19:"home_banner1_images";s:5:"value";s:13:"["920","919"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-17 16:39:01";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:64;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:87;s:4:"type";s:18:"home_banner1_links";s:5:"value";s:101:"["https:\/\/netbazzar.com\/users\/login","https:\/\/netbazzar.com\/shop\/registration\/verification"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-17 16:41:06";}s:11:" * original";a:6:{s:2:"id";i:87;s:4:"type";s:18:"home_banner1_links";s:5:"value";s:101:"["https:\/\/netbazzar.com\/users\/login","https:\/\/netbazzar.com\/shop\/registration\/verification"]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-17 16:41:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:65;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:88;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:2:"[]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:88;s:4:"type";s:19:"home_banner2_images";s:5:"value";s:2:"[]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:66;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:89;s:4:"type";s:18:"home_banner2_links";s:5:"value";s:2:"[]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:89;s:4:"type";s:18:"home_banner2_links";s:5:"value";s:2:"[]";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:67;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:90;s:4:"type";s:15:"home_categories";s:5:"value";s:17:"["1","2","4","5"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:25:37";}s:11:" * original";a:6:{s:2:"id";i:90;s:4:"type";s:15:"home_categories";s:5:"value";s:17:"["1","2","4","5"]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:25:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:68;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:91;s:4:"type";s:16:"top10_categories";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:91;s:4:"type";s:16:"top10_categories";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:69;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:92;s:4:"type";s:12:"top10_brands";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:92;s:4:"type";s:12:"top10_brands";s:5:"value";s:2:"[]";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:70;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:93;s:4:"type";s:12:"website_name";s:5:"value";s:65:"Dropshipping & Wholesale B2B For India | Fast & Reliable Sourcing";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:11:" * original";a:6:{s:2:"id";i:93;s:4:"type";s:12:"website_name";s:5:"value";s:65:"Dropshipping & Wholesale B2B For India | Fast & Reliable Sourcing";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:71;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:94;s:4:"type";s:10:"site_motto";s:5:"value";s:33:"Quality  & Made in India Products";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:11:" * original";a:6:{s:2:"id";i:94;s:4:"type";s:10:"site_motto";s:5:"value";s:33:"Quality  & Made in India Products";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:72;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:95;s:4:"type";s:9:"site_icon";s:5:"value";s:3:"923";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:53:20";}s:11:" * original";a:6:{s:2:"id";i:95;s:4:"type";s:9:"site_icon";s:5:"value";s:3:"923";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:53:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:73;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:96;s:4:"type";s:10:"base_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:96;s:4:"type";s:10:"base_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:74;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:97;s:4:"type";s:14:"base_hov_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:97;s:4:"type";s:14:"base_hov_color";s:5:"value";s:7:"#e62e04";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:75;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:98;s:4:"type";s:10:"meta_title";s:5:"value";s:85:"Netbazzar: India's Top Dropshipping Platform for Online Sellers & Wholesale Suppliers";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:11:" * original";a:6:{s:2:"id";i:98;s:4:"type";s:10:"meta_title";s:5:"value";s:85:"Netbazzar: India's Top Dropshipping Platform for Online Sellers & Wholesale Suppliers";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:76;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:99;s:4:"type";s:16:"meta_description";s:5:"value";s:213:"Netbazzar is India's leading dropshipping platform. Empowering Indian online sellers to start and grow their e-commerce business with no inventory or warehousing. Connects sellers with trusted wholesale suppliers.";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:11:" * original";a:6:{s:2:"id";i:99;s:4:"type";s:16:"meta_description";s:5:"value";s:213:"Netbazzar is India's leading dropshipping platform. Empowering Indian online sellers to start and grow their e-commerce business with no inventory or warehousing. Connects sellers with trusted wholesale suppliers.";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:77;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:100;s:4:"type";s:13:"meta_keywords";s:5:"value";s:614:"how to start dropshipping in India
best dropshipping products to sell in India
find wholesale suppliers for online store India
dropshipping for beginners India
no inventory online business India
benefits of dropshipping in India
dropshipping vs traditional e-commerce India
product sourcing for online sellers India
how to fulfill orders with dropshipping India
download product photos for online selling India
wholesale rates for online resellers India
sell on Amazon India without inventory
sell on Flipkart without inventory
Indian e-commerce wholesale market
supplier fulfilled dropshipping India";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:11:" * original";a:6:{s:2:"id";i:100;s:4:"type";s:13:"meta_keywords";s:5:"value";s:614:"how to start dropshipping in India
best dropshipping products to sell in India
find wholesale suppliers for online store India
dropshipping for beginners India
no inventory online business India
benefits of dropshipping in India
dropshipping vs traditional e-commerce India
product sourcing for online sellers India
how to fulfill orders with dropshipping India
download product photos for online selling India
wholesale rates for online resellers India
sell on Amazon India without inventory
sell on Flipkart without inventory
Indian e-commerce wholesale market
supplier fulfilled dropshipping India";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:23:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:78;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:101;s:4:"type";s:10:"meta_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:101;s:4:"type";s:10:"meta_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:79;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:102;s:4:"type";s:9:"site_name";s:5:"value";s:28:"Netbazzar Dropshipping India";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:11:" * original";a:6:{s:2:"id";i:102;s:4:"type";s:9:"site_name";s:5:"value";s:28:"Netbazzar Dropshipping India";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-09 18:17:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:80;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:103;s:4:"type";s:17:"system_logo_white";s:5:"value";s:3:"923";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:53:20";}s:11:" * original";a:6:{s:2:"id";i:103;s:4:"type";s:17:"system_logo_white";s:5:"value";s:3:"923";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:53:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:81;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:104;s:4:"type";s:17:"system_logo_black";s:5:"value";s:3:"924";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:56:23";}s:11:" * original";a:6:{s:2:"id";i:104;s:4:"type";s:17:"system_logo_black";s:5:"value";s:3:"924";s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2025-06-20 16:56:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:82;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:105;s:4:"type";s:8:"timezone";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:105;s:4:"type";s:8:"timezone";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:83;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:106;s:4:"type";s:22:"admin_login_background";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:11:" * original";a:6:{s:2:"id";i:106;s:4:"type";s:22:"admin_login_background";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2020-11-16 12:56:36";s:10:"updated_at";s:19:"2020-11-16 12:56:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:84;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:107;s:4:"type";s:14:"iyzico_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:11:" * original";a:6:{s:2:"id";i:107;s:4:"type";s:14:"iyzico_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:85;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:109;s:4:"type";s:17:"decimal_separator";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:11:" * original";a:6:{s:2:"id";i:109;s:4:"type";s:17:"decimal_separator";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2020-12-30 22:15:56";s:10:"updated_at";s:19:"2020-12-30 22:15:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:86;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:112;s:4:"type";s:13:"bkash_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:11:" * original";a:6:{s:2:"id";i:112;s:4:"type";s:13:"bkash_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-01-22 16:00:03";s:10:"updated_at";s:19:"2021-01-22 16:00:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:87;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:113;s:4:"type";s:18:"header_menu_labels";s:5:"value";s:36:"["Home","Liquidation Stock","Blogs"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-04-11 16:33:21";}s:11:" * original";a:6:{s:2:"id";i:113;s:4:"type";s:18:"header_menu_labels";s:5:"value";s:36:"["Home","Liquidation Stock","Blogs"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-04-11 16:33:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:88;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:114;s:4:"type";s:17:"header_menu_links";s:5:"value";s:89:"["https:\/\/netbazzar.com\/","https:\/\/netbazzar.com\/","https:\/\/netbazzar.com\/blog"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-06-09 18:29:13";}s:11:" * original";a:6:{s:2:"id";i:114;s:4:"type";s:17:"header_menu_links";s:5:"value";s:89:"["https:\/\/netbazzar.com\/","https:\/\/netbazzar.com\/","https:\/\/netbazzar.com\/blog"]";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2025-06-09 18:29:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:89;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:115;s:4:"type";s:8:"proxypay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:11:" * original";a:6:{s:2:"id";i:115;s:4:"type";s:8:"proxypay";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:90;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:116;s:4:"type";s:16:"proxypay_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:11:" * original";a:6:{s:2:"id";i:116;s:4:"type";s:16:"proxypay_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-06-20 16:55:42";s:10:"updated_at";s:19:"2021-06-20 16:55:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:91;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:117;s:4:"type";s:10:"google_map";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:11:" * original";a:6:{s:2:"id";i:117;s:4:"type";s:10:"google_map";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:92;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:118;s:4:"type";s:15:"google_firebase";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:11:" * original";a:6:{s:2:"id";i:118;s:4:"type";s:15:"google_firebase";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2021-07-27 21:19:39";s:10:"updated_at";s:19:"2021-07-27 21:19:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:93;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:119;s:4:"type";s:20:"authorizenet_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2021-06-14 10:30:23";}s:11:" * original";a:6:{s:2:"id";i:119;s:4:"type";s:20:"authorizenet_sandbox";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2021-02-16 08:13:11";s:10:"updated_at";s:19:"2021-06-14 10:30:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:94;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:120;s:4:"type";s:30:"min_order_amount_check_activat";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:120;s:4:"type";s:30:"min_order_amount_check_activat";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:95;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:121;s:4:"type";s:20:"minimum_order_amount";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:121;s:4:"type";s:20:"minimum_order_amount";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:96;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:122;s:4:"type";s:9:"item_name";s:5:"value";s:9:"eCommerce";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:122;s:4:"type";s:9:"item_name";s:5:"value";s:9:"eCommerce";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:97;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:124;s:4:"type";s:16:"aamarpay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:11:" * original";a:6:{s:2:"id";i:124;s:4:"type";s:16:"aamarpay_sandbox";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2022-04-17 12:27:17";s:10:"updated_at";s:19:"2022-04-17 12:27:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:98;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:125;s:4:"type";s:20:"secondary_base_color";s:5:"value";s:7:"#13814C";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:125;s:4:"type";s:20:"secondary_base_color";s:5:"value";s:7:"#13814C";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:99;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:126;s:4:"type";s:24:"secondary_base_hov_color";s:5:"value";s:7:"#0f6f41";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:126;s:4:"type";s:24:"secondary_base_hov_color";s:5:"value";s:7:"#0f6f41";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:100;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:127;s:4:"type";s:20:"header_nav_menu_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-09-09 16:26:18";}s:11:" * original";a:6:{s:2:"id";i:127;s:4:"type";s:20:"header_nav_menu_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2024-09-09 16:26:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:101;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:128;s:4:"type";s:15:"homepage_select";s:5:"value";s:7:"classic";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:128;s:4:"type";s:15:"homepage_select";s:5:"value";s:7:"classic";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:102;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:129;s:4:"type";s:22:"todays_deal_section_bg";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:129;s:4:"type";s:22:"todays_deal_section_bg";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:103;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:130;s:4:"type";s:28:"todays_deal_section_bg_color";s:5:"value";s:7:"#3d4666";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:130;s:4:"type";s:28:"todays_deal_section_bg_color";s:5:"value";s:7:"#3d4666";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:104;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:131;s:4:"type";s:19:"flash_deal_bg_color";s:5:"value";s:7:"#d33533";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:131;s:4:"type";s:19:"flash_deal_bg_color";s:5:"value";s:7:"#d33533";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:105;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:132;s:4:"type";s:24:"flash_deal_bg_full_width";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:132;s:4:"type";s:24:"flash_deal_bg_full_width";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:106;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:133;s:4:"type";s:27:"flash_deal_banner_menu_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:133;s:4:"type";s:27:"flash_deal_banner_menu_text";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:107;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:134;s:4:"type";s:29:"todays_deal_banner_text_color";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:134;s:4:"type";s:29:"todays_deal_banner_text_color";s:5:"value";s:5:"light";s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:108;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:135;s:4:"type";s:23:"coupon_background_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:11:" * original";a:6:{s:2:"id";i:135;s:4:"type";s:23:"coupon_background_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-11-19 15:15:29";s:10:"updated_at";s:19:"2023-11-19 15:15:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:109;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:136;s:4:"type";s:22:"admin_login_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:27";}s:11:" * original";a:6:{s:2:"id";i:136;s:4:"type";s:22:"admin_login_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:110;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:137;s:4:"type";s:25:"customer_login_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:16";}s:11:" * original";a:6:{s:2:"id";i:137;s:4:"type";s:25:"customer_login_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:111;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:138;s:4:"type";s:28:"customer_register_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:16";}s:11:" * original";a:6:{s:2:"id";i:138;s:4:"type";s:28:"customer_register_page_image";s:5:"value";s:3:"876";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:25:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:112;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:139;s:4:"type";s:23:"seller_login_page_image";s:5:"value";s:3:"877";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:42:39";}s:11:" * original";a:6:{s:2:"id";i:139;s:4:"type";s:23:"seller_login_page_image";s:5:"value";s:3:"877";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:42:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:113;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:140;s:4:"type";s:26:"seller_register_page_image";s:5:"value";s:3:"878";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:49:25";}s:11:" * original";a:6:{s:2:"id";i:140;s:4:"type";s:26:"seller_register_page_image";s:5:"value";s:3:"878";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:49:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:114;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:141;s:4:"type";s:29:"delivery_boy_login_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:141;s:4:"type";s:29:"delivery_boy_login_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:115;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:142;s:4:"type";s:26:"forgot_password_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:142;s:4:"type";s:26:"forgot_password_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:116;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:143;s:4:"type";s:25:"password_reset_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:143;s:4:"type";s:25:"password_reset_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:117;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:144;s:4:"type";s:30:"phone_number_verify_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:144;s:4:"type";s:30:"phone_number_verify_page_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:118;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:145;s:4:"type";s:28:"authentication_layout_select";s:5:"value";s:4:"free";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:01:59";}s:11:" * original";a:6:{s:2:"id";i:145;s:4:"type";s:28:"authentication_layout_select";s:5:"value";s:4:"free";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2025-04-15 16:01:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:119;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:146;s:4:"type";s:24:"flash_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:146;s:4:"type";s:24:"flash_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:120;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:147;s:4:"type";s:24:"flash_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:147;s:4:"type";s:24:"flash_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:121;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:148;s:4:"type";s:27:"flash_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:148;s:4:"type";s:27:"flash_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:122;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:149;s:4:"type";s:20:"flash_deal_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:149;s:4:"type";s:20:"flash_deal_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:123;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:150;s:4:"type";s:25:"todays_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:150;s:4:"type";s:25:"todays_deal_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:124;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:151;s:4:"type";s:25:"todays_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:151;s:4:"type";s:25:"todays_deal_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:125;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:152;s:4:"type";s:28:"todays_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:152;s:4:"type";s:28:"todays_deal_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:126;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:153;s:4:"type";s:21:"todays_deal_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:153;s:4:"type";s:21:"todays_deal_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:127;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:154;s:4:"type";s:25:"new_product_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:154;s:4:"type";s:25:"new_product_card_bg_image";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:128;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:155;s:4:"type";s:25:"new_product_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:155;s:4:"type";s:25:"new_product_card_bg_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:129;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:156;s:4:"type";s:28:"new_product_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:156;s:4:"type";s:28:"new_product_card_bg_subtitle";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:130;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:157;s:4:"type";s:21:"new_product_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:157;s:4:"type";s:21:"new_product_card_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:131;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:158;s:4:"type";s:24:"featured_categories_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:11:" * original";a:6:{s:2:"id";i:158;s:4:"type";s:24:"featured_categories_text";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2023-12-11 15:25:33";s:10:"updated_at";s:19:"2023-12-11 15:25:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:132;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:159;s:4:"type";s:25:"guest_checkout_activation";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-03-05 18:05:24";s:10:"updated_at";s:19:"2024-03-05 18:05:24";}s:11:" * original";a:6:{s:2:"id";i:159;s:4:"type";s:25:"guest_checkout_activation";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-03-05 18:05:24";s:10:"updated_at";s:19:"2024-03-05 18:05:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:133;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:160;s:4:"type";s:25:"slider_section_full_width";s:5:"value";s:1:"0";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:160;s:4:"type";s:25:"slider_section_full_width";s:5:"value";s:1:"0";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:134;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:161;s:4:"type";s:23:"slider_section_bg_color";s:5:"value";s:7:"#dedede";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:161;s:4:"type";s:23:"slider_section_bg_color";s:5:"value";s:7:"#dedede";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:135;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:162;s:4:"type";s:19:"home_banner4_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:162;s:4:"type";s:19:"home_banner4_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:136;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:163;s:4:"type";s:18:"home_banner4_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:163;s:4:"type";s:18:"home_banner4_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:137;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:164;s:4:"type";s:19:"home_banner5_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:164;s:4:"type";s:19:"home_banner5_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:138;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:165;s:4:"type";s:18:"home_banner5_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:165;s:4:"type";s:18:"home_banner5_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:139;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:166;s:4:"type";s:19:"home_banner6_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:166;s:4:"type";s:19:"home_banner6_images";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:140;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:167;s:4:"type";s:18:"home_banner6_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:11:" * original";a:6:{s:2:"id";i:167;s:4:"type";s:18:"home_banner6_links";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-03-19 11:42:16";s:10:"updated_at";s:19:"2024-03-19 11:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:141;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:168;s:4:"type";s:30:"last_viewed_product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-02 12:29:06";s:10:"updated_at";s:19:"2024-04-02 12:29:06";}s:11:" * original";a:6:{s:2:"id";i:168;s:4:"type";s:30:"last_viewed_product_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-02 12:29:06";s:10:"updated_at";s:19:"2024-04-02 12:29:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:142;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:169;s:4:"type";s:21:"custom_alert_location";s:5:"value";s:11:"bottom-left";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-03 09:00:20";s:10:"updated_at";s:19:"2024-04-03 09:00:20";}s:11:" * original";a:6:{s:2:"id";i:169;s:4:"type";s:21:"custom_alert_location";s:5:"value";s:11:"bottom-left";s:4:"lang";N;s:10:"created_at";s:19:"2024-04-03 09:00:20";s:10:"updated_at";s:19:"2024-04-03 09:00:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:143;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:170;s:4:"type";s:22:"notification_show_type";s:5:"value";s:8:"design_2";s:4:"lang";N;s:10:"created_at";s:19:"2024-06-11 12:55:53";s:10:"updated_at";s:19:"2024-09-14 14:11:38";}s:11:" * original";a:6:{s:2:"id";i:170;s:4:"type";s:22:"notification_show_type";s:5:"value";s:8:"design_2";s:4:"lang";N;s:10:"created_at";s:19:"2024-06-11 12:55:53";s:10:"updated_at";s:19:"2024-09-14 14:11:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:144;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:171;s:4:"type";s:16:"cupon_text_color";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:171;s:4:"type";s:16:"cupon_text_color";s:5:"value";s:4:"dark";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:145;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:172;s:4:"type";s:26:"flash_deal_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:172;s:4:"type";s:26:"flash_deal_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:146;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:173;s:4:"type";s:32:"flash_deal_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:173;s:4:"type";s:32:"flash_deal_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:147;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:174;s:4:"type";s:25:"featured_section_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:174;s:4:"type";s:25:"featured_section_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:148;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:175;s:4:"type";s:24:"featured_section_outline";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:175;s:4:"type";s:24:"featured_section_outline";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:149;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:176;s:4:"type";s:30:"featured_section_outline_color";s:5:"value";s:7:"#dfdfe6";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:176;s:4:"type";s:30:"featured_section_outline_color";s:5:"value";s:7:"#dfdfe6";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:150;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:177;s:4:"type";s:29:"best_selling_section_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:177;s:4:"type";s:29:"best_selling_section_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:151;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:178;s:4:"type";s:28:"best_selling_section_outline";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:178;s:4:"type";s:28:"best_selling_section_outline";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:152;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:179;s:4:"type";s:34:"best_selling_section_outline_color";s:5:"value";s:7:"#dfdfe6";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:179;s:4:"type";s:34:"best_selling_section_outline_color";s:5:"value";s:7:"#dfdfe6";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:153;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:180;s:4:"type";s:29:"new_products_section_bg_color";s:5:"value";s:7:"#f5f5f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:180;s:4:"type";s:29:"new_products_section_bg_color";s:5:"value";s:7:"#f5f5f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:154;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:181;s:4:"type";s:28:"new_products_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:181;s:4:"type";s:28:"new_products_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:155;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:182;s:4:"type";s:34:"new_products_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:182;s:4:"type";s:34:"new_products_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:156;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:183;s:4:"type";s:32:"home_categories_section_bg_color";s:5:"value";s:7:"#f2f4f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:183;s:4:"type";s:32:"home_categories_section_bg_color";s:5:"value";s:7:"#f2f4f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:157;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:184;s:4:"type";s:32:"home_categories_content_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:184;s:4:"type";s:32:"home_categories_content_bg_color";s:5:"value";s:7:"#ffffff";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:158;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:185;s:4:"type";s:31:"home_categories_content_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:185;s:4:"type";s:31:"home_categories_content_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:159;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:186;s:4:"type";s:37:"home_categories_content_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:186;s:4:"type";s:37:"home_categories_content_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:160;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:187;s:4:"type";s:27:"classified_section_bg_color";s:5:"value";s:7:"#fff9ed";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:187;s:4:"type";s:27:"classified_section_bg_color";s:5:"value";s:7:"#fff9ed";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:161;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:188;s:4:"type";s:26:"classified_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:188;s:4:"type";s:26:"classified_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:162;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:189;s:4:"type";s:32:"classified_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:189;s:4:"type";s:32:"classified_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:163;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:190;s:4:"type";s:24:"sellers_section_bg_color";s:5:"value";s:7:"#fff9ed";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:190;s:4:"type";s:24:"sellers_section_bg_color";s:5:"value";s:7:"#fff9ed";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:164;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:191;s:4:"type";s:23:"sellers_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:191;s:4:"type";s:23:"sellers_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:165;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:192;s:4:"type";s:29:"sellers_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:192;s:4:"type";s:29:"sellers_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:166;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:193;s:4:"type";s:23:"brands_section_bg_color";s:5:"value";s:7:"#f0f2f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:193;s:4:"type";s:23:"brands_section_bg_color";s:5:"value";s:7:"#f0f2f5";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:167;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:194;s:4:"type";s:22:"brands_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:194;s:4:"type";s:22:"brands_section_outline";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:168;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:195;s:4:"type";s:28:"brands_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:195;s:4:"type";s:28:"brands_section_outline_color";s:5:"value";s:7:"#000000";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:169;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:196;s:4:"type";s:21:"uploaded_image_format";s:5:"value";s:7:"default";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:11:" * original";a:6:{s:2:"id";i:196;s:4:"type";s:21:"uploaded_image_format";s:5:"value";s:7:"default";s:4:"lang";N;s:10:"created_at";s:19:"2024-07-14 11:46:47";s:10:"updated_at";s:19:"2024-07-14 11:46:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:170;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:197;s:4:"type";s:32:"product_external_link_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:11:" * original";a:6:{s:2:"id";i:197;s:4:"type";s:32:"product_external_link_for_seller";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:171;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:198;s:4:"type";s:20:"use_floating_buttons";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:11:" * original";a:6:{s:2:"id";i:198;s:4:"type";s:20:"use_floating_buttons";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:172;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:199;s:4:"type";s:22:"seller_commission_type";s:5:"value";s:10:"fixed_rate";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:11:" * original";a:6:{s:2:"id";i:199;s:4:"type";s:22:"seller_commission_type";s:5:"value";s:10:"fixed_rate";s:4:"lang";N;s:10:"created_at";s:19:"2024-08-22 11:15:04";s:10:"updated_at";s:19:"2024-08-22 11:15:04";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:173;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:200;s:4:"type";s:13:"purchase_code";s:5:"value";s:36:"c9016c61-0214-4fff-9ba0-9a5ede7d1213";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 12:41:06";s:10:"updated_at";s:19:"2024-09-09 12:41:06";}s:11:" * original";a:6:{s:2:"id";i:200;s:4:"type";s:13:"purchase_code";s:5:"value";s:36:"c9016c61-0214-4fff-9ba0-9a5ede7d1213";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 12:41:06";s:10:"updated_at";s:19:"2024-09-09 12:41:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:174;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:201;s:4:"type";s:13:"topbar_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:11:" * original";a:6:{s:2:"id";i:201;s:4:"type";s:13:"topbar_banner";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:175;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:202;s:4:"type";s:20:"topbar_banner_medium";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:11:" * original";a:6:{s:2:"id";i:202;s:4:"type";s:20:"topbar_banner_medium";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:176;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:203;s:4:"type";s:19:"topbar_banner_small";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:11:" * original";a:6:{s:2:"id";i:203;s:4:"type";s:19:"topbar_banner_small";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:177;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:204;s:4:"type";s:18:"topbar_banner_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:11:" * original";a:6:{s:2:"id";i:204;s:4:"type";s:18:"topbar_banner_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:178;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:205;s:4:"type";s:15:"helpline_number";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:11:" * original";a:6:{s:2:"id";i:205;s:4:"type";s:15:"helpline_number";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:26:09";s:10:"updated_at";s:19:"2024-09-09 16:26:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:179;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:206;s:4:"type";s:18:"todays_deal_banner";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 17:23:00";}s:11:" * original";a:6:{s:2:"id";i:206;s:4:"type";s:18:"todays_deal_banner";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 17:23:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:180;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:207;s:4:"type";s:24:"todays_deal_banner_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 16:39:59";}s:11:" * original";a:6:{s:2:"id";i:207;s:4:"type";s:24:"todays_deal_banner_small";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 16:39:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:181;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:208;s:4:"type";s:20:"todays_deal_bg_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 16:39:59";}s:11:" * original";a:6:{s:2:"id";i:208;s:4:"type";s:20:"todays_deal_bg_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:39:59";s:10:"updated_at";s:19:"2024-09-09 16:39:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:182;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:209;s:4:"type";s:12:"footer_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:43:32";s:10:"updated_at";s:19:"2024-09-14 11:10:45";}s:11:" * original";a:6:{s:2:"id";i:209;s:4:"type";s:12:"footer_title";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:43:32";s:10:"updated_at";s:19:"2024-09-14 11:10:45";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:183;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:210;s:4:"type";s:18:"footer_description";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:43:32";s:10:"updated_at";s:19:"2024-09-14 11:10:45";}s:11:" * original";a:6:{s:2:"id";i:210;s:4:"type";s:18:"footer_description";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:43:32";s:10:"updated_at";s:19:"2024-09-14 11:10:45";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:184;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:211;s:4:"type";s:20:"about_us_description";s:5:"value";s:331:"<p>Netbazzar is a dedicated B2B marketplace designed to connect Indian manufacturers with e-commerce sellers. We provide the tools and platform to list, discover, and resell products online, eliminating the need for inventory and warehousing.</p><p>                                        
                                    </p>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2025-04-12 11:30:50";}s:11:" * original";a:6:{s:2:"id";i:211;s:4:"type";s:20:"about_us_description";s:5:"value";s:331:"<p>Netbazzar is a dedicated B2B marketplace designed to connect Indian manufacturers with e-commerce sellers. We provide the tools and platform to list, discover, and resell products online, eliminating the need for inventory and warehousing.</p><p>                                        
                                    </p>";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2025-04-12 11:30:50";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:185;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:212;s:4:"type";s:15:"play_store_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2024-09-09 16:46:14";}s:11:" * original";a:6:{s:2:"id";i:212;s:4:"type";s:15:"play_store_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2024-09-09 16:46:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:186;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:213;s:4:"type";s:14:"app_store_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2024-09-09 16:46:14";}s:11:" * original";a:6:{s:2:"id";i:213;s:4:"type";s:14:"app_store_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 16:46:14";s:10:"updated_at";s:19:"2024-09-09 16:46:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:187;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:214;s:4:"type";s:30:"minimum_seller_amount_withdraw";s:5:"value";s:4:"1000";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 17:17:43";s:10:"updated_at";s:19:"2024-09-09 17:17:43";}s:11:" * original";a:6:{s:2:"id";i:214;s:4:"type";s:30:"minimum_seller_amount_withdraw";s:5:"value";s:4:"1000";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 17:17:43";s:10:"updated_at";s:19:"2024-09-09 17:17:43";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:188;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:215;s:4:"type";s:28:"vendor_commission_activation";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 17:41:38";s:10:"updated_at";s:19:"2024-09-09 17:41:41";}s:11:" * original";a:6:{s:2:"id";i:215;s:4:"type";s:28:"vendor_commission_activation";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-09 17:41:38";s:10:"updated_at";s:19:"2024-09-09 17:41:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:189;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:216;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-13 10:43:48";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:11:" * original";a:6:{s:2:"id";i:216;s:4:"type";s:23:"frontend_copyright_text";s:5:"value";N;s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-13 10:43:48";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:190;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:217;s:4:"type";s:15:"seller_app_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 10:43:48";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:11:" * original";a:6:{s:2:"id";i:217;s:4:"type";s:15:"seller_app_link";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 10:43:48";s:10:"updated_at";s:19:"2024-09-13 10:43:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:191;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:218;s:4:"type";s:19:"use_image_watermark";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-14 11:44:47";}s:11:" * original";a:6:{s:2:"id";i:218;s:4:"type";s:19:"use_image_watermark";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-14 11:44:47";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:192;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:219;s:4:"type";s:20:"image_watermark_type";s:5:"value";s:5:"image";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:11:" * original";a:6:{s:2:"id";i:219;s:4:"type";s:20:"image_watermark_type";s:5:"value";s:5:"image";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:193;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:220;s:4:"type";s:15:"watermark_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-14 11:04:07";}s:11:" * original";a:6:{s:2:"id";i:220;s:4:"type";s:15:"watermark_image";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-14 11:04:07";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:194;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:221;s:4:"type";s:14:"watermark_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:11:" * original";a:6:{s:2:"id";i:221;s:4:"type";s:14:"watermark_text";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:195;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:222;s:4:"type";s:19:"watermark_text_size";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:11:" * original";a:6:{s:2:"id";i:222;s:4:"type";s:19:"watermark_text_size";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:196;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:223;s:4:"type";s:20:"watermark_text_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:11:" * original";a:6:{s:2:"id";i:223;s:4:"type";s:20:"watermark_text_color";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:197;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:224;s:4:"type";s:18:"watermark_position";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:11:" * original";a:6:{s:2:"id";i:224;s:4:"type";s:18:"watermark_position";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:40:22";s:10:"updated_at";s:19:"2024-09-13 16:40:22";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:198;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:225;s:4:"type";s:13:"header_script";s:5:"value";s:295:"<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-2ZK2LR1HXP"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-2ZK2LR1HXP');
</script>";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:45:05";s:10:"updated_at";s:19:"2024-09-13 17:07:16";}s:11:" * original";a:6:{s:2:"id";i:225;s:4:"type";s:13:"header_script";s:5:"value";s:295:"<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-2ZK2LR1HXP"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-2ZK2LR1HXP');
</script>";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:45:05";s:10:"updated_at";s:19:"2024-09-13 17:07:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:199;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:226;s:4:"type";s:13:"footer_script";s:5:"value";s:246:"<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MWC4S34Q"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:45:05";s:10:"updated_at";s:19:"2024-09-13 16:52:59";}s:11:" * original";a:6:{s:2:"id";i:226;s:4:"type";s:13:"footer_script";s:5:"value";s:246:"<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MWC4S34Q"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-13 16:45:05";s:10:"updated_at";s:19:"2024-09-13 16:52:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:200;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:227;s:4:"type";s:22:"cookies_agreement_text";s:5:"value";s:317:"<p>NetBazzar uses cookies to enhance your experience, personalize content, and analyze website traffic. By using our website, you agree to our use of cookies in accordance with our <a rel="noopener" href="#">Privacy Policy</a>. You can manage your cookie preferences at any time through your browser settings.<br></p>";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:12:15";s:10:"updated_at";s:19:"2024-09-14 11:12:15";}s:11:" * original";a:6:{s:2:"id";i:227;s:4:"type";s:22:"cookies_agreement_text";s:5:"value";s:317:"<p>NetBazzar uses cookies to enhance your experience, personalize content, and analyze website traffic. By using our website, you agree to our use of cookies in accordance with our <a rel="noopener" href="#">Privacy Policy</a>. You can manage your cookie preferences at any time through your browser settings.<br></p>";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:12:15";s:10:"updated_at";s:19:"2024-09-14 11:12:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:201;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:228;s:4:"type";s:22:"show_cookies_agreement";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:12:16";s:10:"updated_at";s:19:"2024-09-14 11:12:16";}s:11:" * original";a:6:{s:2:"id";i:228;s:4:"type";s:22:"show_cookies_agreement";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:12:16";s:10:"updated_at";s:19:"2024-09-14 11:12:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:202;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:229;s:4:"type";s:24:"seller_wholesale_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:45:36";s:10:"updated_at";s:19:"2024-09-14 11:45:36";}s:11:" * original";a:6:{s:2:"id";i:229;s:4:"type";s:24:"seller_wholesale_product";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2024-09-14 11:45:36";s:10:"updated_at";s:19:"2024-09-14 11:45:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:203;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:230;s:4:"type";s:15:"contact_address";s:5:"value";s:79:"549 E Central Ave, CA, 92705  or 406,Hilltown Landmark, Nikol, Ahmedabad-382350";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-18 10:50:25";s:10:"updated_at";s:19:"2025-06-09 18:15:20";}s:11:" * original";a:6:{s:2:"id";i:230;s:4:"type";s:15:"contact_address";s:5:"value";s:79:"549 E Central Ave, CA, 92705  or 406,Hilltown Landmark, Nikol, Ahmedabad-382350";s:4:"lang";s:2:"en";s:10:"created_at";s:19:"2024-09-18 10:50:25";s:10:"updated_at";s:19:"2025-06-09 18:15:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:204;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:231;s:4:"type";s:24:"product_approve_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:24";s:10:"updated_at";s:19:"2025-01-02 17:08:24";}s:11:" * original";a:6:{s:2:"id";i:231;s:4:"type";s:24:"product_approve_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:24";s:10:"updated_at";s:19:"2025-01-02 17:08:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:205;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:232;s:4:"type";s:23:"product_manage_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:25";s:10:"updated_at";s:19:"2025-01-02 17:08:25";}s:11:" * original";a:6:{s:2:"id";i:232;s:4:"type";s:23:"product_manage_by_admin";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:25";s:10:"updated_at";s:19:"2025-01-02 17:08:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:206;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:233;s:4:"type";s:24:"product_query_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:27";s:10:"updated_at";s:19:"2025-01-02 17:08:27";}s:11:" * original";a:6:{s:2:"id";i:233;s:4:"type";s:24:"product_query_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-01-02 17:08:27";s:10:"updated_at";s:19:"2025-01-02 17:08:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:207;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:234;s:4:"type";s:13:"whatsapp_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:16:51";s:10:"updated_at";s:19:"2025-04-09 16:16:51";}s:11:" * original";a:6:{s:2:"id";i:234;s:4:"type";s:13:"whatsapp_chat";s:5:"value";s:1:"0";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:16:51";s:10:"updated_at";s:19:"2025-04-09 16:16:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:208;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:235;s:4:"type";s:28:"customer_registration_verify";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:22:39";s:10:"updated_at";s:19:"2025-04-09 16:22:39";}s:11:" * original";a:6:{s:2:"id";i:235;s:4:"type";s:28:"customer_registration_verify";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:22:39";s:10:"updated_at";s:19:"2025-04-09 16:22:39";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:209;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:236;s:4:"type";s:26:"seller_registration_verify";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:22:40";s:10:"updated_at";s:19:"2025-04-09 16:22:40";}s:11:" * original";a:6:{s:2:"id";i:236;s:4:"type";s:26:"seller_registration_verify";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-09 16:22:40";s:10:"updated_at";s:19:"2025-04-09 16:22:40";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:210;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:237;s:4:"type";s:21:"newsletter_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-15 15:11:54";s:10:"updated_at";s:19:"2025-04-15 15:11:54";}s:11:" * original";a:6:{s:2:"id";i:237;s:4:"type";s:21:"newsletter_activation";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-04-15 15:11:54";s:10:"updated_at";s:19:"2025-04-15 15:11:54";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:211;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:238;s:4:"type";s:18:"show_website_popup";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:11:" * original";a:6:{s:2:"id";i:238;s:4:"type";s:18:"show_website_popup";s:5:"value";s:2:"on";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:212;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:239;s:4:"type";s:21:"website_popup_content";s:5:"value";s:72632:"<div _ngcontent-ng-c2599284398="" class="markdown markdown-main-panel enable-updated-hr-color" id="model-response-message-contentr_bd7294d861aafd49" dir="ltr" style="--animation-duration: 400ms; --fade-animation-function: linear; animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; color: rgb(27, 28, 29); columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; font-family: &quot;Google Sans Text&quot;, sans-serif !important; line-height: 1.15 !important;"><p data-sourcepos="1:1-1:50" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Google Sans&quot;, sans-serif; font-size: 2.5rem;">Netbazzar: Your Gateway to Profitable Online Selling in India</span></p><p data-sourcepos="5:1-5:354" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Are you an aspiring Indian online seller looking to launch your e-commerce business without the headaches of inventory and warehousing? Or are you an existing online seller seeking to expand your product catalog and boost your profits? Look no further than Netbazzar – India's premier dropshipping platform for online sellers and wholesale suppliers.</strong></p><h2 data-sourcepos="7:1-7:22" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; font-family: &quot;Google Sans&quot;, sans-serif !important; line-height: 1.15 !important;">For Online Sellers:</h2><p data-sourcepos="9:1-9:62" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Unlock Your E-commerce Potential with Zero Inventory Risk!</strong></p><p data-sourcepos="11:1-11:156" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;">Netbazzar empowers you to become a successful online retailer with unparalleled ease and flexibility. We provide everything you need to start selling today:</p><ul data-sourcepos="13:1-23:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="13:1-13:209" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Vast Product Catalog:</strong> Gain access to a diverse range of high-quality products from trusted wholesale suppliers across India. From electronics to fashion, home goods to accessories, we've got you covered.</li>
<li data-sourcepos="14:1-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Effortless Product Listing:</strong>
<ul data-sourcepos="15:5-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="15:5-15:146" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Download Product Assets:</strong> Instantly download high-resolution product photos, detailed descriptions, and up-to-date inventory information.</li>
<li data-sourcepos="16:5-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell Anywhere Online:</strong> Seamlessly list our products on your preferred online selling platforms like Amazon, Flipkart, your own e-commerce store (Shopify, WooCommerce, etc.), social media, and more!</li>
</ul>
</li>
<li data-sourcepos="17:1-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Hassle-Free Order Fulfillment:</strong>
<ul data-sourcepos="18:5-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="18:5-18:147" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell First, Buy Later:</strong> Once you receive an order from your customer, simply place the order on Netbazzar at our exclusive wholesale cost.</li>
<li data-sourcepos="19:5-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">We Handle the Shipping:</strong> We take care of packaging and directly shipping the product to your customer. No need to touch a single item!</li>
</ul>
</li>
<li data-sourcepos="20:1-20:176" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">No Inventory, No Warehousing:</strong> Say goodbye to upfront investment in stock, storage costs, and managing logistics. With Netbazzar, you operate on a pure dropshipping model.</li>
<li data-sourcepos="21:1-21:149" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Positive Cash Flow:</strong> You only pay for the product after your customer has paid you, ensuring a healthy and positive cash flow for your business.</li>
<li data-sourcepos="22:1-23:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Focus on Growth:</strong> Dedicate your time and energy to marketing, customer service, and growing your online presence, while we handle the backend operations.</li>
</ul><h2 data-sourcepos="24:1-24:27" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; font-family: &quot;Google Sans&quot;, sans-serif !important; line-height: 1.15 !important;">For Wholesale Suppliers:</h2><p data-sourcepos="26:1-26:64" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Expand Your Reach and Maximize Your Sales with Dropshipping!</strong></p><p data-sourcepos="28:1-28:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;">Netbazzar offers a robust platform for wholesale suppliers to connect with a vast network of online resellers across India. Partner with us to:</p><ul data-sourcepos="30:1-35:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="30:1-30:146" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Reach a Wider Audience:</strong> Access thousands of online sellers actively looking for products to sell, expanding your market reach exponentially.</li>
<li data-sourcepos="31:1-31:115" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Increase Sales Volume:</strong> Drive significant sales without the need for extensive marketing efforts on your part.</li>
<li data-sourcepos="32:1-32:139" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Efficient Order Management:</strong> Receive streamlined orders from our platform, making it easy to manage and fulfill dropshipping requests.</li>
<li data-sourcepos="33:1-33:142" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Reduce Marketing Costs:</strong> Leverage our network of resellers to promote your products, effectively reducing your own marketing expenditure.</li>
<li data-sourcepos="34:1-35:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Focus on Your Core Business:</strong> Concentrate on manufacturing and sourcing while we handle the distribution channel for your products.</li>
</ul><hr data-sourcepos="36:1-36:3" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><p data-sourcepos="38:1-38:43" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">How Netbazzar Works for Online Sellers:</strong></p><ol data-sourcepos="40:1-46:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 28px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="40:1-40:95" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Browse &amp; Select:</strong> Explore our extensive catalog of products from various categories.</li>
<li data-sourcepos="41:1-41:137" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Download &amp; List:</strong> Download product details, images, and inventory data, and list them on your chosen online selling platforms.</li>
<li data-sourcepos="42:1-42:93" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell &amp; Earn:</strong> Market your products and start receiving orders from your customers.</li>
<li data-sourcepos="43:1-43:115" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Order on Netbazzar:</strong> When you get an order, simply place the same order on Netbazzar at the wholesale price.</li>
<li data-sourcepos="44:1-44:152" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">We Ship Directly:</strong> We ship the product directly to your customer, branded neutrally or with your chosen branding (if applicable and agreed upon).</li>
<li data-sourcepos="45:1-46:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Profit!</strong> You keep the difference between your selling price and our wholesale cost.</li>
</ol><hr data-sourcepos="47:1-47:3" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><p data-sourcepos="49:1-49:82" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Ready to start your profitable online journey or expand your supplier network?</strong></p><p data-sourcepos="55:1-55:60" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Netbazzar: Simplifying E-commerce for a Brighter Future.</strong></p></div>";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:11:" * original";a:6:{s:2:"id";i:239;s:4:"type";s:21:"website_popup_content";s:5:"value";s:72632:"<div _ngcontent-ng-c2599284398="" class="markdown markdown-main-panel enable-updated-hr-color" id="model-response-message-contentr_bd7294d861aafd49" dir="ltr" style="--animation-duration: 400ms; --fade-animation-function: linear; animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; color: rgb(27, 28, 29); columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; font-family: &quot;Google Sans Text&quot;, sans-serif !important; line-height: 1.15 !important;"><p data-sourcepos="1:1-1:50" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Google Sans&quot;, sans-serif; font-size: 2.5rem;">Netbazzar: Your Gateway to Profitable Online Selling in India</span></p><p data-sourcepos="5:1-5:354" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Are you an aspiring Indian online seller looking to launch your e-commerce business without the headaches of inventory and warehousing? Or are you an existing online seller seeking to expand your product catalog and boost your profits? Look no further than Netbazzar – India's premier dropshipping platform for online sellers and wholesale suppliers.</strong></p><h2 data-sourcepos="7:1-7:22" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; font-family: &quot;Google Sans&quot;, sans-serif !important; line-height: 1.15 !important;">For Online Sellers:</h2><p data-sourcepos="9:1-9:62" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Unlock Your E-commerce Potential with Zero Inventory Risk!</strong></p><p data-sourcepos="11:1-11:156" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;">Netbazzar empowers you to become a successful online retailer with unparalleled ease and flexibility. We provide everything you need to start selling today:</p><ul data-sourcepos="13:1-23:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="13:1-13:209" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Vast Product Catalog:</strong> Gain access to a diverse range of high-quality products from trusted wholesale suppliers across India. From electronics to fashion, home goods to accessories, we've got you covered.</li>
<li data-sourcepos="14:1-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Effortless Product Listing:</strong>
<ul data-sourcepos="15:5-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="15:5-15:146" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Download Product Assets:</strong> Instantly download high-resolution product photos, detailed descriptions, and up-to-date inventory information.</li>
<li data-sourcepos="16:5-16:206" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell Anywhere Online:</strong> Seamlessly list our products on your preferred online selling platforms like Amazon, Flipkart, your own e-commerce store (Shopify, WooCommerce, etc.), social media, and more!</li>
</ul>
</li>
<li data-sourcepos="17:1-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Hassle-Free Order Fulfillment:</strong>
<ul data-sourcepos="18:5-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="18:5-18:147" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell First, Buy Later:</strong> Once you receive an order from your customer, simply place the order on Netbazzar at our exclusive wholesale cost.</li>
<li data-sourcepos="19:5-19:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">We Handle the Shipping:</strong> We take care of packaging and directly shipping the product to your customer. No need to touch a single item!</li>
</ul>
</li>
<li data-sourcepos="20:1-20:176" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">No Inventory, No Warehousing:</strong> Say goodbye to upfront investment in stock, storage costs, and managing logistics. With Netbazzar, you operate on a pure dropshipping model.</li>
<li data-sourcepos="21:1-21:149" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Positive Cash Flow:</strong> You only pay for the product after your customer has paid you, ensuring a healthy and positive cash flow for your business.</li>
<li data-sourcepos="22:1-23:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Focus on Growth:</strong> Dedicate your time and energy to marketing, customer service, and growing your online presence, while we handle the backend operations.</li>
</ul><h2 data-sourcepos="24:1-24:27" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; font-family: &quot;Google Sans&quot;, sans-serif !important; line-height: 1.15 !important;">For Wholesale Suppliers:</h2><p data-sourcepos="26:1-26:64" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Expand Your Reach and Maximize Your Sales with Dropshipping!</strong></p><p data-sourcepos="28:1-28:143" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;">Netbazzar offers a robust platform for wholesale suppliers to connect with a vast network of online resellers across India. Partner with us to:</p><ul data-sourcepos="30:1-35:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 27px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="30:1-30:146" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Reach a Wider Audience:</strong> Access thousands of online sellers actively looking for products to sell, expanding your market reach exponentially.</li>
<li data-sourcepos="31:1-31:115" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Increase Sales Volume:</strong> Drive significant sales without the need for extensive marketing efforts on your part.</li>
<li data-sourcepos="32:1-32:139" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Efficient Order Management:</strong> Receive streamlined orders from our platform, making it easy to manage and fulfill dropshipping requests.</li>
<li data-sourcepos="33:1-33:142" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Reduce Marketing Costs:</strong> Leverage our network of resellers to promote your products, effectively reducing your own marketing expenditure.</li>
<li data-sourcepos="34:1-35:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Focus on Your Core Business:</strong> Concentrate on manufacturing and sourcing while we handle the distribution channel for your products.</li>
</ul><hr data-sourcepos="36:1-36:3" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><p data-sourcepos="38:1-38:43" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">How Netbazzar Works for Online Sellers:</strong></p><ol data-sourcepos="40:1-46:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 28px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; padding-inline-start: 32px; line-height: 1.15 !important;">
<li data-sourcepos="40:1-40:95" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Browse &amp; Select:</strong> Explore our extensive catalog of products from various categories.</li>
<li data-sourcepos="41:1-41:137" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Download &amp; List:</strong> Download product details, images, and inventory data, and list them on your chosen online selling platforms.</li>
<li data-sourcepos="42:1-42:93" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Sell &amp; Earn:</strong> Market your products and start receiving orders from your customers.</li>
<li data-sourcepos="43:1-43:115" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Order on Netbazzar:</strong> When you get an order, simply place the same order on Netbazzar at the wholesale price.</li>
<li data-sourcepos="44:1-44:152" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">We Ship Directly:</strong> We ship the product directly to your customer, branded neutrally or with your chosen branding (if applicable and agreed upon).</li>
<li data-sourcepos="45:1-46:0" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px 0px 0px 4px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Profit!</strong> You keep the difference between your selling price and our wholesale cost.</li>
</ol><hr data-sourcepos="47:1-47:3" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 8px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;"><p data-sourcepos="49:1-49:82" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Ready to start your profitable online journey or expand your supplier network?</strong></p><p data-sourcepos="55:1-55:60" style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 16px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; line-height: 1.15 !important;"><strong style="animation: 0s ease 0s 1 normal none running none; appearance: none; background: none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0); border: 0px none rgb(27, 28, 29); inset: auto; clear: none; clip: auto; columns: auto; contain: none; container: none; content: normal; cursor: auto; cx: 0px; cy: 0px; d: none; direction: ltr; display: inline; fill: rgb(0, 0, 0); filter: none; flex: 0 1 auto; float: none; gap: normal; hyphens: manual; interactivity: auto; isolation: auto; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; marker: none; mask: none; offset: normal; opacity: 1; order: 0; outline: rgb(27, 28, 29) none 0px; overlay: none; padding: 0px; page: auto; perspective: none; position: static; quotes: auto; r: 0px; resize: none; rotate: none; rx: auto; ry: auto; scale: none; speak: normal; stroke: none; transform: none; transition: all; translate: none; visibility: visible; x: 0px; y: 0px; zoom: 1; margin-top: 0px !important; line-height: 1.15 !important;">Netbazzar: Simplifying E-commerce for a Brighter Future.</strong></p></div>";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:213;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:240;s:4:"type";s:19:"show_subscribe_form";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:11:" * original";a:6:{s:2:"id";i:240;s:4:"type";s:19:"show_subscribe_form";s:5:"value";N;s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:24:58";s:10:"updated_at";s:19:"2025-06-09 18:24:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:214;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:241;s:4:"type";s:10:"top_brands";s:5:"value";s:5:"["3"]";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:25:45";s:10:"updated_at";s:19:"2025-06-09 18:25:45";}s:11:" * original";a:6:{s:2:"id";i:241;s:4:"type";s:10:"top_brands";s:5:"value";s:5:"["3"]";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-09 18:25:45";s:10:"updated_at";s:19:"2025-06-09 18:25:45";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:215;O:26:"App\Models\BusinessSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"business_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:242;s:4:"type";s:26:"disable_image_optimization";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-12 16:52:24";s:10:"updated_at";s:19:"2025-06-12 16:52:24";}s:11:" * original";a:6:{s:2:"id";i:242;s:4:"type";s:26:"disable_image_optimization";s:5:"value";s:1:"1";s:4:"lang";N;s:10:"created_at";s:19:"2025-06-12 16:52:24";s:10:"updated_at";s:19:"2025-06-12 16:52:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}