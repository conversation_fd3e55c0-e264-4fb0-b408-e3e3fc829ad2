ALTER TABLE `users` ADD COLUMN `seller_unique_id` VA<PERSON>HAR(255) NULL UNIQUE AFTER `id`;
ALTER TABLE `products` ADD COLUMN `unique_sku` VARCHAR(255) NULL UNIQUE AFTER `id`;

CREATE TABLE `saved_items` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `product_id` BIGINT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    UNIQUE KEY `saved_items_user_id_product_id_unique` (`user_id`, `product_id`),
    CONSTRAINT `saved_items_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `saved_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
);

CREATE TABLE `saved_items` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `product_id` BIGINT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    UNIQUE KEY `saved_items_user_id_product_id_unique` (`user_id`, `product_id`)
    -- Foreign keys removed for testing
);

ALTER TABLE `orders`
ADD COLUMN `shipping_label` VARCHAR(255) NULL AFTER `shipping_type`;

