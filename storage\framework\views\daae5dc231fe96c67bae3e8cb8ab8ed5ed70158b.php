

<?php $__env->startSection('meta_title'); ?><?php echo e($shop->meta_title); ?><?php $__env->stopSection(); ?>

<?php $__env->startSection('meta_description'); ?><?php echo e($shop->meta_description); ?><?php $__env->stopSection(); ?>

<?php $__env->startSection('meta'); ?>
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="<?php echo e($shop->meta_title); ?>">
    <meta itemprop="description" content="<?php echo e($shop->meta_description); ?>">
    <meta itemprop="image" content="<?php echo e(uploaded_asset($shop->logo)); ?>">

    <!-- Twitter Card data -->
    <meta name="twitter:card" content="website">
    <meta name="twitter:site" content="@publisher_handle">
    <meta name="twitter:title" content="<?php echo e($shop->meta_title); ?>">
    <meta name="twitter:description" content="<?php echo e($shop->meta_description); ?>">
    <meta name="twitter:creator" content="@author_handle">
    <meta name="twitter:image" content="<?php echo e(uploaded_asset($shop->meta_img)); ?>">

    <!-- Open Graph data -->
    <meta property="og:title" content="<?php echo e($shop->meta_title); ?>" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<?php echo e(route('shop.visit', $shop->slug)); ?>" />
    <meta property="og:image" content="<?php echo e(uploaded_asset($shop->logo)); ?>" />
    <meta property="og:description" content="<?php echo e($shop->meta_description); ?>" />
    <meta property="og:site_name" content="<?php echo e($shop->name); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section class="mt-3 mb-3 bg-white">
        <div class="container">
            <!--  Top Menu -->
            <div class="d-flex flex-wrap justify-content-center justify-content-md-start">
                <a class="fw-700 fs-11 fs-md-13 mr-3 mr-sm-4 mr-md-5 text-dark opacity-60 hov-opacity-100 <?php if(!isset($type)): ?> opacity-100 <?php endif; ?>"
                        href="<?php echo e(route('shop.visit', $shop->slug)); ?>"><?php echo e(translate('Store Home')); ?></a>
                <a class="fw-700 fs-11 fs-md-13 mr-3 mr-sm-4 mr-md-5 text-dark opacity-60 hov-opacity-100 <?php if(isset($type) && $type == 'top-selling'): ?> opacity-100 <?php endif; ?>"
                        href="<?php echo e(route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'top-selling'])); ?>"><?php echo e(translate('Top Selling')); ?></a>
                <a class="fw-700 fs-11 fs-md-13 mr-3 mr-sm-4 mr-md-5 text-dark opacity-60 hov-opacity-100 <?php if(isset($type) && $type == 'cupons'): ?> opacity-100 <?php endif; ?>"
                        href="<?php echo e(route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons'])); ?>"><?php echo e(translate('Coupons')); ?></a>

                <a class="fw-700 fs-11 fs-md-13 text-dark mr-3 mr-sm-4 mr-md-5 opacity-60 hov-opacity-100 <?php if(isset($type) && $type == 'all-products'): ?> opacity-100 <?php endif; ?>"
                        href="<?php echo e(route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products'])); ?>"><?php echo e(translate('All Products')); ?></a>

                <?php if(addon_is_activated('preorder')): ?>
                <a class="fw-700 fs-11 fs-md-13 mr-3 mr-sm-4 mr-md-5 text-dark opacity-60 hov-opacity-100 <?php if(isset($type) && $type == 'all-preorder-products'): ?> opacity-100 <?php endif; ?>"
                        href="<?php echo e(route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-preorder-products'])); ?>"><?php echo e(translate('All Preorder Products')); ?></a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <?php
        $followed_sellers = [];
        if (Auth::check()) {
            $followed_sellers = get_followed_sellers();
        }
    ?>

    <?php if(!isset($type) || $type == 'top-selling' || $type == 'cupons'): ?>
        <?php if($shop->top_banner_image): ?>
            <!-- Top Banner -->
            <section class="h-160px h-md-200px h-lg-300px h-xl-100 w-100">
                <a href="<?php echo e($shop->top_banner_link); ?>">
                    <img class="d-block lazyload h-100 img-fit"
                        src="<?php echo e(static_asset('assets/img/placeholder-rect.jpg')); ?>"
                        data-src="<?php echo e(uploaded_asset($shop->top_banner_image)); ?>" alt="<?php echo e(env('APP_NAME')); ?> offer">
                </a>
            </section>
        <?php endif; ?>
    <?php endif; ?>

    <section class="<?php if(!isset($type) || $type == 'top-selling' || $type == 'cupons'): ?> mb-3 <?php endif; ?> border-top border-bottom" style="background: #fcfcfd;">
        <div class="container">
            <!-- Seller Info -->
            <div class="py-4">
                <div class="row justify-content-md-between align-items-center">
                    <div class="col-lg-5 col-md-6">
                        <div class="d-flex align-items-center">
                            <!-- Shop Logo -->
                            <a href="<?php echo e(route('shop.visit', $shop->slug)); ?>" class="overflow-hidden size-64px rounded-content" style="border: 1px solid #e5e5e5;
                                box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.06);min-width: fit-content;">
                                <img class="lazyload h-64px  mx-auto"
                                    src="<?php echo e(static_asset('assets/img/placeholder.jpg')); ?>"
                                    data-src="<?php echo e(uploaded_asset($shop->logo)); ?>"
                                    onerror="this.onerror=null;this.src='<?php echo e(static_asset('assets/img/placeholder.jpg')); ?>';">
                            </a>
                            <div class="ml-3">
                                <!-- Shop Name & Verification Status -->
                                <a href="<?php echo e(route('shop.visit', $shop->slug)); ?>"
                                    class="text-dark d-block fs-16 fw-700">
                                    <?php echo e($shop->name); ?>

                                    <?php if($shop->verification_status == 1): ?>
                                        <span class="ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="17.5" height="17.5" viewBox="0 0 17.5 17.5">
                                                <g id="Group_25616" data-name="Group 25616" transform="translate(-537.249 -1042.75)">
                                                    <path id="Union_5" data-name="Union 5" d="M0,8.75A8.75,8.75,0,1,1,8.75,17.5,8.75,8.75,0,0,1,0,8.75Zm.876,0A7.875,7.875,0,1,0,8.75.875,7.883,7.883,0,0,0,.876,8.75Zm.875,0a7,7,0,1,1,7,7A7.008,7.008,0,0,1,1.751,8.751Zm3.73-.907a.789.789,0,0,0,0,1.115l2.23,2.23a.788.788,0,0,0,1.115,0l3.717-3.717a.789.789,0,0,0,0-1.115.788.788,0,0,0-1.115,0l-3.16,3.16L6.6,7.844a.788.788,0,0,0-1.115,0Z" transform="translate(537.249 1042.75)" fill="#3490f3"/>
                                                </g>
                                            </svg>
                                        </span>
                                    <?php else: ?>
                                        <span class="ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="17.5" height="17.5" viewBox="0 0 17.5 17.5">
                                                <g id="Group_25616" data-name="Group 25616" transform="translate(-537.249 -1042.75)">
                                                    <path id="Union_5" data-name="Union 5" d="M0,8.75A8.75,8.75,0,1,1,8.75,17.5,8.75,8.75,0,0,1,0,8.75Zm.876,0A7.875,7.875,0,1,0,8.75.875,7.883,7.883,0,0,0,.876,8.75Zm.875,0a7,7,0,1,1,7,7A7.008,7.008,0,0,1,1.751,8.751Zm3.73-.907a.789.789,0,0,0,0,1.115l2.23,2.23a.788.788,0,0,0,1.115,0l3.717-3.717a.789.789,0,0,0,0-1.115.788.788,0,0,0-1.115,0l-3.16,3.16L6.6,7.844a.788.788,0,0,0-1.115,0Z" transform="translate(537.249 1042.75)" fill="red"/>
                                                </g>
                                            </svg>
                                        </span>
                                    <?php endif; ?>
                                </a>
                                <!-- Ratting -->
                                <div class="rating rating-mr-2 text-dark">
                                    <?php echo e(renderStarRating($shop->rating)); ?>

                                    <span class="opacity-60 fs-12">(<?php echo e($shop->num_of_reviews); ?>

                                        <?php echo e(translate('Reviews')); ?>)</span>
                                </div>
                                <!-- Address -->
                                <div class="location fs-12 opacity-70 text-dark mt-1"><?php echo e($shop->address); ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col pl-5 pl-md-0 ml-5 ml-md-0">
                        <div class="d-lg-flex align-items-center justify-content-lg-end">
                            <div class="d-md-flex justify-content-md-end align-items-md-baseline">
                                <!-- Member Since -->
                                <div class="pr-md-3 mt-2 mt-md-0 border-md-right">
                                    <div class="fs-10 fw-400 text-secondary"><?php echo e(translate('Member Since')); ?></div>
                                    <div class="mt-1 fs-16 fw-700 text-secondary"><?php echo e(date('d M Y',strtotime($shop->created_at))); ?></div>
                                </div>
                                <!-- Social Links -->
                                <?php if($shop->facebook || $shop->instagram || $shop->google || $shop->twitter || $shop->youtube): ?>
                                    <div class="pl-md-3 pr-lg-3 mt-2 mt-md-0 border-lg-right">
                                        <span class="fs-10 fw-400 text-secondary"><?php echo e(translate('Social Media')); ?></span><br>
                                        <ul class="social-md colored-light list-inline mb-0 mt-1">
                                            <?php if($shop->facebook): ?>
                                            <li class="list-inline-item mr-2">
                                                <a href="<?php echo e($shop->facebook); ?>" class="facebook"
                                                    target="_blank">
                                                    <i class="lab la-facebook-f"></i>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($shop->instagram): ?>
                                            <li class="list-inline-item mr-2">
                                                <a href="<?php echo e($shop->instagram); ?>" class="instagram"
                                                    target="_blank">
                                                    <i class="lab la-instagram"></i>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($shop->google): ?>
                                            <li class="list-inline-item mr-2">
                                                <a href="<?php echo e($shop->google); ?>" class="google"
                                                    target="_blank">
                                                    <i class="lab la-google"></i>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($shop->twitter): ?>
                                            <li class="list-inline-item mr-2">
                                                <a href="<?php echo e($shop->twitter); ?>" class="twitter"
                                                    target="_blank">
                                                    <i class="lab la-twitter"></i>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                            <?php if($shop->youtube): ?>
                                            <li class="list-inline-item">
                                                <a href="<?php echo e($shop->youtube); ?>" class="youtube"
                                                    target="_blank">
                                                    <i class="lab la-youtube"></i>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <!-- follow -->
                            <div class="d-flex justify-content-md-end pl-lg-3 pt-3 pt-lg-0">
                                <?php $shopFollowers = count($shop->followers) + $shop->custom_followers; ?>
                                <?php if(in_array($shop->id, $followed_sellers)): ?>
                                    <a href="<?php echo e(route("followed_seller.remove", ['id'=>$shop->id])); ?>"  data-toggle="tooltip" data-title="<?php echo e(translate('Unfollow Seller')); ?>" data-placement="top"
                                        class="btn btn-success d-flex align-items-center justify-content-center fs-12 w-190px follow-btn followed"
                                        style="height: 40px; border-radius: 30px !important; justify-content: center;">
                                        <i class="las la-check fs-16 mr-2"></i>
                                        <span class="fw-700"><?php echo e(translate('Followed')); ?></span> &nbsp; (<?php echo e($shopFollowers); ?>)
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo e(route("followed_seller.store", ['id'=>$shop->id])); ?>"
                                        class="btn btn-primary d-flex align-items-center justify-content-center fs-12 w-190px follow-btn"
                                        style="height: 40px; border-radius: 30px !important; justify-content: center;">
                                        <i class="las la-plus fs-16 mr-2"></i>
                                        <span class="fw-700"><?php echo e(translate('Follow Seller')); ?></span> &nbsp; (<?php echo e($shopFollowers); ?>)
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php if(!isset($type)): ?>

        <!-- Featured Products -->
        <?php
            $feature_products = $shop->user->products->where('published', 1)->where('approved', 1)->where('seller_featured', 1);
        ?>
        <?php if(count($feature_products) > 0): ?>
            <section class="mt-3 mb-3" id="section_featured">
                <div class="container">
                <!-- Top Section -->
                <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-16 fs-md-20 fw-700 mb-3 mb-sm-0">
                            <span class=""><?php echo e(translate('Featured Products')); ?></span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex">
                            <a type="button" class="arrow-prev slide-arrow text-secondary mr-2" onclick="clickToSlide('slick-prev','section_featured')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_featured')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Products Section -->
                    <div class="px-sm-3">
                        <div class="aiz-carousel sm-gutters-16 arrow-none" data-items="6" data-xl-items="5" data-lg-items="4"  data-md-items="3" data-sm-items="2" data-xs-items="2" data-arrows='true' data-autoplay='true' data-infinute="true">
                            <?php $__currentLoopData = $feature_products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="carousel-box px-3 position-relative has-transition hov-animate-outline border-right border-top border-bottom <?php if($key == 0): ?> border-left <?php endif; ?>">
                                <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <!-- Banner Slider -->
        <?php if($shop->slider_images != null): ?>
            <section class="mt-3 mb-3">
                <div class="container">
                    <div class="aiz-carousel mobile-img-auto-height" data-arrows="true" data-dots="false" data-autoplay="true">
                        <?php
                            $shop_slider_images = get_slider_images(json_decode($shop->slider_images, true));
                            $shop_slider_links = json_decode($shop->slider_links, true);
                        ?>
                        <?php $__currentLoopData = $shop_slider_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="carousel-box w-100 h-140px h-md-300px h-xl-450px">
                                <a href="<?php echo e(isset($shop_slider_links[$key]) ? $shop_slider_links[$key] : ''); ?>">
                                    <img class="d-block lazyload h-100 img-fit" 
                                        src="<?php echo e($slider ? my_asset($slider->file_name) : static_asset('assets/img/placeholder.jpg')); ?>"
                                        onerror="this.onerror=null;this.src='<?php echo e(static_asset('assets/img/placeholder-rect.jpg')); ?>';"
                                        alt="<?php echo e($key); ?> offer">
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>


        <!-- Coupons -->
        <?php
            $coupons = get_coupons($shop->user->id);
        ?>
        <?php if(count($coupons)>0): ?>
            <section class="mt-3 mb-3" id="section_coupons">
                <div class="container">
                <!-- Top Section -->
                <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-16 fs-md-20 fw-700 mb-3 mb-sm-0">
                            <span class="pb-3"><?php echo e(translate('Coupons')); ?></span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex">
                            <a type="button" class="arrow-prev slide-arrow link-disable text-secondary mr-2" onclick="clickToSlide('slick-prev','section_coupons')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a class="text-blue fs-12 fw-700 hov-text-primary" href="<?php echo e(route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons'])); ?>"><?php echo e(translate('View All')); ?></a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_coupons')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Coupons Section -->
                    <div class="aiz-carousel sm-gutters-16 arrow-none" data-items="3" data-lg-items="2" data-sm-items="1" data-arrows='true' data-infinite='false'>
                        <?php $__currentLoopData = $coupons->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="carousel-box">
                                <?php echo $__env->make('frontend.partials.coupon_box',['coupon' => $coupon], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
        
        <!-- Banner full width 1 -->
        <?php if($shop->banner_full_width_1_images): ?>
            <?php
                $shop_banner_full_width_1_images = get_slider_images(json_decode($shop->banner_full_width_1_images, true));
                $shop_banner_full_width_1_links = json_decode($shop->banner_full_width_1_links, true);
            ?>
            <?php $__currentLoopData = $shop_banner_full_width_1_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <section class="container mb-3 mt-3">
                    <div class="w-100">
                        <a href="<?php echo e(isset($shop_banner_full_width_1_links[$key]) ? $shop_banner_full_width_1_links[$key] : ''); ?>">
                            <img class="d-block lazyload h-100 img-fit"
                                src="<?php echo e($banner ? my_asset($banner->file_name) : static_asset('assets/img/placeholder.jpg')); ?>"
                                onerror="this.onerror=null;this.src='<?php echo e(static_asset('assets/img/placeholder-rect.jpg')); ?>';"
                                alt="<?php echo e(env('APP_NAME')); ?> banner">
                        </a>
                    </div>
                </section>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
        
        <!-- Banner half width -->
        <?php if($shop->banners_half_width_images): ?>
            <?php
                $shop_banners_half_width_images = get_slider_images(json_decode($shop->banners_half_width_images, true));
                $shop_banners_half_width_links = json_decode($shop->banners_half_width_links, true);
            ?>
            <section class="container  mb-3 mt-3">
                <div class="row gutters-16">
                    <?php $__currentLoopData = $shop_banners_half_width_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-6 mb-3 mb-md-0">
                        <div class="w-100">
                            <a href="<?php echo e(isset($shop_banners_half_width_links[$key]) ? $shop_banners_half_width_links[$key] : ''); ?>">
                                <img class="d-block lazyload h-100 img-fit"
                                    src="<?php echo e($banner ? my_asset($banner->file_name) : static_asset('assets/img/placeholder.jpg')); ?>"
                                    onerror="this.onerror=null;this.src='<?php echo e(static_asset('assets/img/placeholder-rect.jpg')); ?>';"
                                    alt="<?php echo e(env('APP_NAME')); ?> banner">
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </section>
        <?php endif; ?>

    <?php endif; ?>

    <section class="mb-3 mt-3" id="section_types">
        <div class="container">
            <!-- Top Section -->
            <div class="d-flex mb-4 align-items-baseline justify-content-between">
                <!-- Title -->
                <h3 class="fs-16 fs-md-20 fw-700 mb-3 mb-sm-0">
                    <span class="pb-3">
                        <?php if(!isset($type)): ?>
                            <?php echo e(translate('New Arrival Products')); ?>

                        <?php elseif($type == 'top-selling'): ?>
                            <?php echo e(translate('Top Selling')); ?>

                        <?php elseif($type == 'cupons'): ?>
                            <?php echo e(translate('All Cupons')); ?>

                        <?php endif; ?>
                    </span>
                </h3>
                <?php if(!isset($type)): ?>
                    <!-- Links -->
                    <div class="d-flex">
                        <a type="button" class="arrow-prev slide-arrow link-disable text-secondary mr-2" onclick="clickToSlide('slick-prev','section_types')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                        <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_types')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                    </div>
                <?php endif; ?>
            </div>

            <?php
                if (!isset($type)){
                    $products = get_seller_products($shop->user->id);
                }
                elseif ($type == 'top-selling'){
                    $products = get_shop_best_selling_products($shop->user->id);
                }
                elseif ($type == 'cupons'){
                    $coupons = get_coupons($shop->user->id , 24);
                }
            ?>

            <?php if(!isset($type)): ?>
                <!-- New Arrival Products Section -->
                <div class="px-sm-3 pb-3">
                    <div class="aiz-carousel sm-gutters-16 arrow-none" data-items="6" data-xl-items="5" data-lg-items="4"  data-md-items="3" data-sm-items="2" data-xs-items="2" data-arrows='true' data-infinite='false'>
                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="carousel-box px-3 position-relative has-transition hov-animate-outline border-right border-top border-bottom <?php if($key == 0): ?> border-left <?php endif; ?>">
                            <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Banner full width 2 -->
                <?php if($shop->banner_full_width_2_images): ?>
                    <?php
                        $shop_banner_full_width_2_images = get_slider_images(json_decode($shop->banner_full_width_2_images, true));
                        $shop_banner_full_width_2_links = json_decode($shop->banner_full_width_2_links, true);
                    ?>
                    <?php $__currentLoopData = $shop_banner_full_width_2_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="mt-3 mb-3 w-100">
                            <a href="<?php echo e(isset($shop_banner_full_width_2_links[$key]) ? $shop_banner_full_width_2_links[$key] : ''); ?>">
                                <img class="d-block lazyload h-100 img-fit"
                                    src="<?php echo e($banner ? my_asset($banner->file_name) : static_asset('assets/img/placeholder.jpg')); ?>"
                                    onerror="this.onerror=null;this.src='<?php echo e(static_asset('assets/img/placeholder-rect.jpg')); ?>';"
                                    alt="<?php echo e(env('APP_NAME')); ?> banner">
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>


            <?php elseif($type == 'cupons'): ?>
                <!-- All Coupons Section -->
                <div class="row gutters-16 row-cols-xl-3 row-cols-md-2 row-cols-1">
                    <?php $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col mb-4">
                            <?php echo $__env->make('frontend.partials.coupon_box',['coupon' => $coupon], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    <?php echo e($coupons->links()); ?>

                </div>

            <?php elseif($type == 'all-products'): ?>
                <!-- All Products Section -->
                <form class="" id="search-form" action="" method="GET">
                    <div class="row gutters-16 justify-content-center">
                        <!-- Sidebar -->
                        <div class="col-xl-3 col-md-6 col-sm-8">

                            <!-- Sidebar Filters -->
                            <div class="aiz-filter-sidebar collapse-sidebar-wrap sidebar-xl sidebar-right z-1035">
                                <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" data-same=".filter-sidebar-thumb"></div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <div class="d-flex d-xl-none justify-content-between align-items-center pl-3 border-bottom">
                                        <h3 class="h6 mb-0 fw-600"><?php echo e(translate('Filters')); ?></h3>
                                        <button type="button" class="btn btn-sm p-2 filter-sidebar-thumb" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" >
                                            <i class="las la-times la-2x"></i>
                                        </button>
                                    </div>

                                    <!-- Categories -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_1" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                <?php echo e(translate('Categories')); ?>

                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_1">
                                            <?php $__currentLoopData = get_categories_by_products($shop->user->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="checkbox"
                                                        name="selected_categories[]"
                                                        value="<?php echo e($category->id); ?>" <?php if(in_array($category->id, $selected_categories)): ?> checked <?php endif; ?>
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="fs-14 fw-400 text-dark"><?php echo e($category->getTranslation('name')); ?></span>
                                                </label>
                                                <br>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>

                                    <!-- Price range -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <?php echo e(translate('Price range')); ?>

                                        </div>
                                        <div class="p-3 mr-3">
                                            <div class="aiz-range-slider">
                                                <div
                                                    id="input-slider-range"
                                                    data-range-value-min="<?php if(get_products_count($shop->user->id) < 1): ?> 0 <?php else: ?> <?php echo e(get_product_min_unit_price($shop->user->id)); ?> <?php endif; ?>"
                                                    data-range-value-max="<?php if(get_products_count($shop->user->id) < 1): ?> 0 <?php else: ?> <?php echo e(get_product_max_unit_price($shop->user->id)); ?> <?php endif; ?>"
                                                ></div>

                                                <div class="row mt-2">
                                                    <div class="col-6">
                                                        <span class="range-slider-value value-low fs-14 fw-600 opacity-70"
                                                            <?php if($min_price != null): ?>
                                                                data-range-value-low="<?php echo e($min_price); ?>"
                                                            <?php elseif($products->min('unit_price') > 0): ?>
                                                                data-range-value-low="<?php echo e($products->min('unit_price')); ?>"
                                                            <?php else: ?>
                                                                data-range-value-low="0"
                                                            <?php endif; ?>
                                                            id="input-slider-range-value-low"
                                                        ></span>
                                                    </div>
                                                    <div class="col-6 text-right">
                                                        <span class="range-slider-value value-high fs-14 fw-600 opacity-70"
                                                            <?php if($max_price != null): ?>
                                                                data-range-value-high="<?php echo e($max_price); ?>"
                                                            <?php elseif($products->max('unit_price') > 0): ?>
                                                                data-range-value-high="<?php echo e($products->max('unit_price')); ?>"
                                                            <?php else: ?>
                                                                data-range-value-high="0"
                                                            <?php endif; ?>
                                                            id="input-slider-range-value-high"
                                                        ></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Hidden Items -->
                                        <input type="hidden" name="min_price" value="">
                                        <input type="hidden" name="max_price" value="">
                                    </div>

                                    <!-- Ratings -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_2" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                <?php echo e(translate('Ratings')); ?>

                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_2">
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="5" <?php if($rating==5): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-2"><?php echo e(renderStarRating(5)); ?></span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="4" <?php if($rating==4): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-2"><?php echo e(renderStarRating(4)); ?></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('And Up')); ?></span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="3" <?php if($rating==3): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-2"><?php echo e(renderStarRating(3)); ?></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('And Up')); ?></span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="2" <?php if($rating==2): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-2"><?php echo e(renderStarRating(2)); ?></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('And Up')); ?></span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="1" <?php if($rating==1): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-2"><?php echo e(renderStarRating(1)); ?></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('And Up')); ?></span>
                                            </label>
                                            <br>
                                        </div>
                                    </div>

                                    <!-- Brands -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_3" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                <?php echo e(translate('Brands')); ?>

                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_3">
                                            <div class="row gutters-10">
                                                <?php $__currentLoopData = get_brands_by_products($shop->user->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-6">
                                                        <label class="aiz-megabox d-block mb-3">
                                                            <input value="<?php echo e($brand->slug); ?>" type="radio" onchange="filter()"
                                                                name="brand" <?php if(isset($brand_id)): ?> <?php if($brand_id == $brand->id): ?> checked <?php endif; ?> <?php endif; ?>>
                                                            <span class="d-block aiz-megabox-elem rounded-0 p-3 border-transparent hov-border-primary">
                                                                <img src="<?php echo e(uploaded_asset($brand->logo)); ?>"
                                                                    class="img-fit mb-2" alt="<?php echo e($brand->getTranslation('name')); ?>">
                                                                <span class="d-block text-center">
                                                                    <span
                                                                        class="d-block fw-400 fs-14"><?php echo e($brand->getTranslation('name')); ?></span>
                                                                </span>
                                                            </span>
                                                        </label>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Contents -->
                        <div class="col-xl-9">
                            <!-- Top Filters -->
                            <div class="text-left mb-2">
                                <div class="row gutters-5 flex-wrap">
                                    <div class="col-lg col-10">
                                        <h1 class="fs-20 fs-md-24 fw-700 text-dark">
                                            <?php echo e(translate('All Products')); ?>

                                        </h1>
                                    </div>
                                    <div class="col-2 col-lg-auto d-xl-none mb-lg-3 text-right">
                                        <button type="button" class="btn btn-icon p-0" data-toggle="class-toggle" data-target=".aiz-filter-sidebar">
                                            <i class="la la-filter la-2x"></i>
                                        </button>
                                    </div>
                                    <div class="col-6 col-lg-auto mb-3 w-lg-200px">
                                        <select class="form-control form-control-sm aiz-selectpicker rounded-0" name="sort_by" onchange="filter()">
                                            <option value=""><?php echo e(translate('Sort by')); ?></option>
                                            <option value="newest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'newest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Newest')); ?></option>
                                            <option value="oldest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'oldest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Oldest')); ?></option>
                                            <option value="price-asc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-asc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price low to high')); ?></option>
                                            <option value="price-desc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-desc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price high to low')); ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Products -->
                            <div class="px-3">
                                <div class="row gutters-16 row-cols-xxl-4 row-cols-xl-3 row-cols-lg-4 row-cols-md-3 row-cols-2 border-top border-left">
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col border-right border-bottom has-transition hov-shadow-out z-1">
                                            <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <div class="aiz-pagination mt-4">
                                <?php echo e($products->appends(request()->input())->links()); ?>

                            </div>
                        </div>
                    </div>
                </form>

            <?php elseif($type == 'all-preorder-products'): ?>
                <!-- All preorder Products Section -->
                <form class="" id="search-form" action="" method="GET">
                    <div class="row gutters-16 justify-content-center">
                        <!-- Sidebar -->
                        <div class="col-xl-3 col-md-6 col-sm-8">

                            <!-- Sidebar Filters -->
                            <div class="aiz-filter-sidebar collapse-sidebar-wrap sidebar-xl sidebar-right z-1035">
                                <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" data-same=".filter-sidebar-thumb"></div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <div class="d-flex d-xl-none justify-content-between align-items-center pl-3 border-bottom">
                                        <h3 class="h6 mb-0 fw-600"><?php echo e(translate('Filters')); ?></h3>
                                        <button type="button" class="btn btn-sm p-2 filter-sidebar-thumb" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" >
                                            <i class="las la-times la-2x"></i>
                                        </button>
                                    </div>

                                        <!-- Categories -->
     
                                <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#collapse_1" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                            <?php echo e(translate('Categories')); ?>

                                        </a>
                                    </div>
                                    <div class="collapse show px-3" id="collapse_1">
                                       
                                        <?php
                                        $product_categories = $type == 'all-preorder-products' ? get_categories_by_preorder_products($shop->user->id) : get_categories_by_products($shop->user->id);
                                        ?>
                                        <?php $__currentLoopData = $product_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="checkbox"
                                                    name="selected_categories[]"
                                                    value="<?php echo e($category->id); ?>" <?php if(in_array($category->id, $selected_categories)): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e($category->getTranslation('name')); ?></span>
                                            </label>
                                            <br>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>

                                 <!-- Attributes -->
                                 <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" 
                                            data-toggle="collapse" data-target="#collapse_availability_filter" style="white-space: normal;">
                                            <?php echo e(translate('Filter by Availability')); ?>

                                        </a>
                                    </div>
                                    <?php
                                        $show = $is_available !== null ? 'show' : '';
                                    ?>
                                    <div class="collapse <?php echo e($show); ?>" id="collapse_availability_filter">
                                        <div class="p-3 aiz-checkbox-list">
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="is_available"
                                                    value="1" <?php if($is_available == 1): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('Available Now')); ?></span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="is_available"
                                                    value="0" <?php if($is_available === '0'): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('Upcoming')); ?></span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="is_available"
                                                    value=""
                                                    <?php if($is_available === null): ?> checked <?php endif; ?>
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark"><?php echo e(translate('All')); ?></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                </div>
                            </div>
                        </div>

                        <!-- Contents -->
                        <div class="col-xl-9">
                            <!-- Top Filters -->
                            <div class="text-left mb-2">
                                <div class="row gutters-5 flex-wrap">
                                    <div class="col-lg col-10">
                                        <h1 class="fs-20 fs-md-24 fw-700 text-dark">
                                            <?php echo e(translate('All Preorder Products')); ?>

                                        </h1>
                                    </div>
                                    <div class="col-2 col-lg-auto d-xl-none mb-lg-3 text-right">
                                        <button type="button" class="btn btn-icon p-0" data-toggle="class-toggle" data-target=".aiz-filter-sidebar">
                                            <i class="la la-filter la-2x"></i>
                                        </button>
                                    </div>
                                    <div class="col-6 col-lg-auto mb-3 w-lg-200px">
                                        <select class="form-control form-control-sm aiz-selectpicker rounded-0" name="sort_by" onchange="filter()">
                                            <option value=""><?php echo e(translate('Sort by')); ?></option>
                                            <option value="newest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'newest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Newest')); ?></option>
                                            <option value="oldest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'oldest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Oldest')); ?></option>
                                            <option value="price-asc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-asc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price low to high')); ?></option>
                                            <option value="price-desc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-desc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price high to low')); ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Products -->
                            <div class="px-3">
                                <div class="row gutters-16 row-cols-xxl-4 row-cols-xl-3 row-cols-lg-4 row-cols-md-3 row-cols-2 border-top border-left">
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col border-right border-bottom has-transition hov-shadow-out z-1">
                                            
                                            <?php echo $__env->make('preorder.frontend.product_box3',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <div class="aiz-pagination mt-4">
                                <?php echo e($products->appends(request()->input())->links()); ?>

                            </div>
                        </div>
                    </div>
                </form>
            <?php else: ?>

                <!-- Top Selling Products Section -->
                <div class="px-3">
                    <div class="row gutters-16 row-cols-xxl-6 row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-2 border-left border-top">
                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col border-bottom border-right overflow-hidden has-transition hov-shadow-out z-1">
                                <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    <?php echo e($products->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </section>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }

        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\NetbazzarB2B\resources\views/frontend/seller_shop.blade.php ENDPATH**/ ?>